import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { DebugProvider, useDebug } from './contexts/DebugContext';
import Layout from './components/layout/Layout';

// Componente para proteger rutas de debug
const DebugRoute = ({ children }) => {
  const { debugMode } = useDebug();
  const navigate = useNavigate();

  useEffect(() => {
    if (!debugMode) {
      navigate('/');
    }
  }, [debugMode, navigate]);

  return debugMode ? children : null;
};

// Componente para aplicar el Layout principal solo a rutas no administrativas
const AppLayout = ({ children }) => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  // Si es una ruta de administración, no aplicar el Layout
  if (isAdminRoute) {
    return children;
  }

  // Para otras rutas, aplicar el Layout
  return <Layout>{children}</Layout>;
};

// Páginas públicas
import Home from './pages/Home';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';

// Páginas protegidas
import Dashboard from './pages/Dashboard';
import AdminDashboard from './pages/admin/AdminDashboard';
import UserManagement from './pages/admin/UserManagement';
import RegistrationCodesPage from './pages/admin/RegistrationCodesPage';
import AcceleratorsPage from './pages/admin/AcceleratorsPage';
import FormBuilderPage from './pages/admin/forms/FormBuilderPage';
import ApplicationsPage from './pages/admin/applications/ApplicationsPage';
import FunnelStagesPage from './pages/admin/funnelStages/FunnelStagesPage';

// Páginas de depuración
import AuthDebugPage from './pages/debug/AuthDebugPage';

function App() {
  return (
    <AuthProvider>
      <DebugProvider>
        <Router>
          <AppLayout>
            <Routes>
              {/* Rutas públicas */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password/:token" element={<ResetPassword />} />

              {/* Rutas protegidas para usuarios normales */}
              <Route element={<ProtectedRoute />}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/profile" element={<div>Perfil (En desarrollo)</div>} />
              </Route>

              {/* Rutas protegidas para administradores */}
              <Route
                element={
                  <ProtectedRoute allowedRoles={['GLOBAL_ADMIN']} />
                }
              >
                <Route path="/admin/dashboard" element={<AdminDashboard />} />
                <Route path="/admin/users" element={<UserManagement />} />
                <Route path="/admin/registration-codes/*" element={<RegistrationCodesPage />} />
                <Route path="/admin/accelerators/*" element={<AcceleratorsPage />} />
                <Route path="/admin/forms/*" element={<FormBuilderPage />} />
                <Route path="/admin/applications/*" element={<ApplicationsPage />} />
                <Route path="/admin/funnel-stages" element={<FunnelStagesPage />} />
                <Route path="/admin/settings" element={<div>Configuración del Sistema (En desarrollo)</div>} />
              </Route>

              {/* Ruta de depuración protegida */}
              <Route
                path="/debug/auth"
                element={
                  <DebugRoute>
                    <AuthDebugPage />
                  </DebugRoute>
                }
              />

              {/* Ruta para manejar rutas no encontradas */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </AppLayout>
        </Router>
      </DebugProvider>
    </AuthProvider>
  );
}

export default App;


