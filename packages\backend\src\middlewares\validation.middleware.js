const { body } = require('express-validator');

// Validaciones para registro de usuario
exports.validateRegister = [
  body('firstName')
    .notEmpty().withMessage('El nombre es requerido')
    .isLength({ min: 2, max: 50 }).withMessage('El nombre debe tener entre 2 y 50 caracteres'),

  body('lastName')
    .notEmpty().withMessage('El apellido es requerido')
    .isLength({ min: 2, max: 50 }).withMessage('El apellido debe tener entre 2 y 50 caracteres'),

  body('email')
    .notEmpty().withMessage('El correo electrónico es requerido')
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('password')
    .notEmpty().withMessage('La contraseña es requerida')
    .isLength({ min: 6 }).withMessage('La contraseña debe tener al menos 6 caracteres'),

  body('registrationCode')
    .notEmpty().withMessage('El código de registro es requerido'),
];

// Validaciones para crear usuario (desde el panel de administración)
exports.validateCreateUser = [
  body('firstName')
    .notEmpty().withMessage('El nombre es requerido')
    .isLength({ min: 2, max: 50 }).withMessage('El nombre debe tener entre 2 y 50 caracteres'),

  body('lastName')
    .notEmpty().withMessage('El apellido es requerido')
    .isLength({ min: 2, max: 50 }).withMessage('El apellido debe tener entre 2 y 50 caracteres'),

  body('email')
    .notEmpty().withMessage('El correo electrónico es requerido')
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('password')
    .notEmpty().withMessage('La contraseña es requerida')
    .isLength({ min: 6 }).withMessage('La contraseña debe tener al menos 6 caracteres'),

  body('roleId')
    .notEmpty().withMessage('El rol es requerido')
    .isInt().withMessage('El rol debe ser un número entero'),

  body('profileData')
    .optional()
    .isObject().withMessage('Los datos de perfil deben ser un objeto válido'),
];

// Validaciones para login
exports.validateLogin = [
  body('email')
    .notEmpty().withMessage('El correo electrónico es requerido')
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('password')
    .notEmpty().withMessage('La contraseña es requerida'),
];

// Validaciones para actualización de usuario
exports.validateUpdateUser = [
  body('firstName')
    .optional()
    .isLength({ min: 2, max: 50 }).withMessage('El nombre debe tener entre 2 y 50 caracteres'),

  body('lastName')
    .optional()
    .isLength({ min: 2, max: 50 }).withMessage('El apellido debe tener entre 2 y 50 caracteres'),

  body('email')
    .optional()
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('roleId')
    .optional()
    .isInt().withMessage('El rol debe ser un número entero'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),

  body('profileData')
    .optional()
    .isObject().withMessage('Los datos de perfil deben ser un objeto válido'),
];

// Validaciones para cambio de contraseña
exports.validateChangePassword = [
  body('currentPassword')
    .notEmpty().withMessage('La contraseña actual es requerida'),

  body('newPassword')
    .notEmpty().withMessage('La nueva contraseña es requerida')
    .isLength({ min: 6 }).withMessage('La nueva contraseña debe tener al menos 6 caracteres'),
];

// Validaciones para solicitud de restablecimiento de contraseña
exports.validateForgotPassword = [
  body('email')
    .notEmpty().withMessage('El correo electrónico es requerido')
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),
];

// Validaciones para restablecimiento de contraseña
exports.validateResetPassword = [
  body('password')
    .notEmpty().withMessage('La nueva contraseña es requerida')
    .isLength({ min: 6 }).withMessage('La nueva contraseña debe tener al menos 6 caracteres'),

  body('confirmPassword')
    .notEmpty().withMessage('La confirmación de contraseña es requerida')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Las contraseñas no coinciden');
      }
      return true;
    }),
];

// Validaciones para creación de aceleradoras
exports.validateCreateAccelerator = [
  body('name')
    .notEmpty().withMessage('El nombre es requerido')
    .isLength({ min: 2, max: 100 }).withMessage('El nombre debe tener entre 2 y 100 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 1000 }).withMessage('La descripción no debe exceder los 1000 caracteres'),

  body('logo')
    .optional(),

  body('website')
    .optional()
    .isURL().withMessage('Debe proporcionar una URL válida'),

  body('location')
    .optional()
    .isLength({ max: 200 }).withMessage('La ubicación no debe exceder los 200 caracteres'),

  body('contactEmail')
    .optional()
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('contactPhone')
    .optional()
    .isLength({ max: 20 }).withMessage('El teléfono no debe exceder los 20 caracteres'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),

  body('additionalData')
    .optional()
    .isObject().withMessage('Los datos adicionales deben ser un objeto válido'),

  body('administratorIds')
    .optional()
    .isArray().withMessage('Los IDs de administradores deben ser un array'),
];

// Validaciones para actualización de aceleradoras
exports.validateUpdateAccelerator = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 }).withMessage('El nombre debe tener entre 2 y 100 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 1000 }).withMessage('La descripción no debe exceder los 1000 caracteres'),

  body('logo')
    .optional(),

  body('website')
    .optional()
    .isURL().withMessage('Debe proporcionar una URL válida'),

  body('location')
    .optional()
    .isLength({ max: 200 }).withMessage('La ubicación no debe exceder los 200 caracteres'),

  body('contactEmail')
    .optional()
    .isEmail().withMessage('Debe proporcionar un correo electrónico válido'),

  body('contactPhone')
    .optional()
    .isLength({ max: 20 }).withMessage('El teléfono no debe exceder los 20 caracteres'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),

  body('additionalData')
    .optional()
    .isObject().withMessage('Los datos adicionales deben ser un objeto válido'),

  body('administratorIds')
    .optional()
    .isArray().withMessage('Los IDs de administradores deben ser un array'),
];

// Validaciones para creación de códigos de registro
exports.validateRegistrationCode = [
  body('roleId')
    .notEmpty().withMessage('El rol es requerido')
    .isInt().withMessage('El rol debe ser un número entero'),

  body('acceleratorId')
    .optional()
    .isInt().withMessage('La aceleradora debe ser un número entero'),

  body('maxUses')
    .optional()
    .isInt({ min: 1 }).withMessage('El máximo de usos debe ser un número entero mayor a 0'),

  body('expiresAt')
    .optional()
    .isISO8601().withMessage('La fecha de expiración debe ser una fecha válida'),

  body('description')
    .optional()
    .isLength({ max: 200 }).withMessage('La descripción no debe exceder los 200 caracteres'),
];

// Validaciones para actualización de códigos de registro
exports.validateUpdateCode = [
  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),

  body('maxUses')
    .optional()
    .isInt({ min: 1 }).withMessage('El máximo de usos debe ser un número entero mayor a 0'),

  body('expiresAt')
    .optional()
    .isISO8601().withMessage('La fecha de expiración debe ser una fecha válida'),

  body('description')
    .optional()
    .isLength({ max: 200 }).withMessage('La descripción no debe exceder los 200 caracteres'),
];

// Validaciones para creación de formularios
exports.validateCreateForm = [
  body('title')
    .notEmpty().withMessage('El título es requerido')
    .isLength({ min: 2, max: 100 }).withMessage('El título debe tener entre 2 y 100 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 1000 }).withMessage('La descripción no debe exceder los 1000 caracteres'),

  body('acceleratorId')
    .notEmpty().withMessage('La aceleradora es requerida')
    .isInt().withMessage('La aceleradora debe ser un número entero'),

  body('formType')
    .optional()
    .isIn(['application', 'evaluation', 'feedback']).withMessage('El tipo de formulario debe ser válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),

  body('startDate')
    .optional()
    .isISO8601().withMessage('La fecha de inicio debe ser una fecha válida'),

  body('endDate')
    .optional()
    .isISO8601().withMessage('La fecha de fin debe ser una fecha válida'),

  body('isPublished')
    .optional()
    .isBoolean().withMessage('El estado de publicación debe ser un valor booleano'),

  body('fields')
    .optional()
    .isArray().withMessage('Los campos deben ser un array'),
];

// Validaciones para actualización de formularios
exports.validateUpdateForm = [
  body('title')
    .optional()
    .isLength({ min: 2, max: 100 }).withMessage('El título debe tener entre 2 y 100 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 1000 }).withMessage('La descripción no debe exceder los 1000 caracteres'),

  body('formType')
    .optional()
    .isIn(['application', 'evaluation', 'feedback']).withMessage('El tipo de formulario debe ser válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),

  body('startDate')
    .optional()
    .isISO8601().withMessage('La fecha de inicio debe ser una fecha válida'),

  body('endDate')
    .optional()
    .isISO8601().withMessage('La fecha de fin debe ser una fecha válida'),

  body('isPublished')
    .optional()
    .isBoolean().withMessage('El estado de publicación debe ser un valor booleano'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),
];

// Validaciones para creación de campos de formulario
exports.validateCreateField = [
  body('formId')
    .notEmpty().withMessage('El formulario es requerido')
    .isInt().withMessage('El formulario debe ser un número entero'),

  body('name')
    .notEmpty().withMessage('El nombre es requerido')
    .isLength({ min: 1, max: 50 }).withMessage('El nombre debe tener entre 1 y 50 caracteres')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('El nombre solo puede contener letras, números y guiones bajos'),

  body('label')
    .notEmpty().withMessage('La etiqueta es requerida')
    .isLength({ min: 1, max: 100 }).withMessage('La etiqueta debe tener entre 1 y 100 caracteres'),

  body('type')
    .notEmpty().withMessage('El tipo es requerido')
    .isIn(['text', 'textarea', 'number', 'email', 'date', 'select', 'radio', 'checkbox', 'file'])
    .withMessage('El tipo debe ser válido'),

  body('placeholder')
    .optional()
    .isLength({ max: 200 }).withMessage('El placeholder no debe exceder los 200 caracteres'),

  body('helpText')
    .optional()
    .isLength({ max: 500 }).withMessage('El texto de ayuda no debe exceder los 500 caracteres'),

  body('required')
    .optional()
    .isBoolean().withMessage('El campo requerido debe ser un valor booleano'),

  body('order')
    .optional()
    .isInt({ min: 0 }).withMessage('El orden debe ser un número entero no negativo'),

  body('options')
    .optional()
    .custom((value, { req }) => {
      if (['select', 'radio', 'checkbox'].includes(req.body.type) && (!value || !Array.isArray(value) || value.length === 0)) {
        throw new Error('Las opciones son requeridas para este tipo de campo');
      }
      return true;
    }),

  body('validations')
    .optional()
    .isObject().withMessage('Las validaciones deben ser un objeto válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),
];

// Validaciones para actualización de campos de formulario
exports.validateUpdateField = [
  body('name')
    .optional()
    .isLength({ min: 1, max: 50 }).withMessage('El nombre debe tener entre 1 y 50 caracteres')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('El nombre solo puede contener letras, números y guiones bajos'),

  body('label')
    .optional()
    .isLength({ min: 1, max: 100 }).withMessage('La etiqueta debe tener entre 1 y 100 caracteres'),

  body('type')
    .optional()
    .isIn(['text', 'textarea', 'number', 'email', 'date', 'select', 'radio', 'checkbox', 'file'])
    .withMessage('El tipo debe ser válido'),

  body('placeholder')
    .optional()
    .isLength({ max: 200 }).withMessage('El placeholder no debe exceder los 200 caracteres'),

  body('helpText')
    .optional()
    .isLength({ max: 500 }).withMessage('El texto de ayuda no debe exceder los 500 caracteres'),

  body('required')
    .optional()
    .isBoolean().withMessage('El campo requerido debe ser un valor booleano'),

  body('order')
    .optional()
    .isInt({ min: 0 }).withMessage('El orden debe ser un número entero no negativo'),

  body('options')
    .optional()
    .custom((value, { req }) => {
      if (req.body.type && ['select', 'radio', 'checkbox'].includes(req.body.type) &&
          (!value || !Array.isArray(value) || value.length === 0)) {
        throw new Error('Las opciones son requeridas para este tipo de campo');
      }
      return true;
    }),

  body('validations')
    .optional()
    .isObject().withMessage('Las validaciones deben ser un objeto válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),
];

// Validaciones para reordenar campos
exports.validateReorderFields = [
  body('fieldOrders')
    .notEmpty().withMessage('Los órdenes de campo son requeridos')
    .isArray().withMessage('Los órdenes de campo deben ser un array')
    .custom(value => {
      if (!value.every(item => item.id && item.order !== undefined)) {
        throw new Error('Cada elemento debe tener un id y un orden');
      }
      return true;
    }),
];

// Validaciones para creación de solicitudes
exports.validateCreateApplication = [
  body('formId')
    .notEmpty().withMessage('El formulario es requerido')
    .isInt().withMessage('El formulario debe ser un número entero'),

  body('responses')
    .notEmpty().withMessage('Las respuestas son requeridas')
    .isObject().withMessage('Las respuestas deben ser un objeto válido'),
];

// Validaciones para actualización de estado de solicitudes
exports.validateUpdateApplicationStatus = [
  body('status')
    .optional()
    .isIn(['pending', 'reviewing', 'approved', 'rejected']).withMessage('El estado debe ser válido'),

  body('funnelStageId')
    .optional()
    .isInt().withMessage('La etapa del embudo debe ser un número entero'),

  body('internalNotes')
    .optional()
    .isString().withMessage('Las notas internas deben ser un texto'),

  body('feedback')
    .optional()
    .isString().withMessage('El feedback debe ser un texto'),

  body('score')
    .optional()
    .isFloat({ min: 0, max: 10 }).withMessage('La puntuación debe ser un número entre 0 y 10'),
];

// Validaciones para creación de etapas del embudo
exports.validateCreateFunnelStage = [
  body('name')
    .notEmpty().withMessage('El nombre es requerido')
    .isLength({ min: 2, max: 50 }).withMessage('El nombre debe tener entre 2 y 50 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 500 }).withMessage('La descripción no debe exceder los 500 caracteres'),

  body('acceleratorId')
    .notEmpty().withMessage('La aceleradora es requerida')
    .isInt().withMessage('La aceleradora debe ser un número entero'),

  body('order')
    .optional()
    .isInt({ min: 1 }).withMessage('El orden debe ser un número entero positivo'),

  body('color')
    .optional()
    .isHexColor().withMessage('El color debe ser un código hexadecimal válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),
];

// Validaciones para actualización de etapas del embudo
exports.validateUpdateFunnelStage = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 50 }).withMessage('El nombre debe tener entre 2 y 50 caracteres'),

  body('description')
    .optional()
    .isLength({ max: 500 }).withMessage('La descripción no debe exceder los 500 caracteres'),

  body('order')
    .optional()
    .isInt({ min: 1 }).withMessage('El orden debe ser un número entero positivo'),

  body('color')
    .optional()
    .isHexColor().withMessage('El color debe ser un código hexadecimal válido'),

  body('config')
    .optional()
    .isObject().withMessage('La configuración debe ser un objeto válido'),

  body('isActive')
    .optional()
    .isBoolean().withMessage('El estado debe ser un valor booleano'),
];

// Validaciones para reordenar etapas del embudo
exports.validateReorderFunnelStages = [
  body('stageOrders')
    .notEmpty().withMessage('Los órdenes de etapa son requeridos')
    .isArray().withMessage('Los órdenes de etapa deben ser un array')
    .custom(value => {
      if (!value.every(item => item.id && item.order !== undefined)) {
        throw new Error('Cada elemento debe tener un id y un orden');
      }
      return true;
    }),
];