import api from './api';

// Servicio para gestión de usuarios
const UserService = {
  // Obtener todos los usuarios (con paginación, filtros y búsqueda)
  getAllUsers: async (page = 1, limit = 10, filters = {}) => {
    try {
      const { search, roleId, status, sortBy, sortOrder } = filters;
      let queryParams = `page=${page}&limit=${limit}`;
      
      if (search) queryParams += `&search=${encodeURIComponent(search)}`;
      if (roleId) queryParams += `&roleId=${roleId}`;
      if (status) queryParams += `&status=${status}`;
      if (sortBy) queryParams += `&sortBy=${sortBy}`;
      if (sortOrder) queryParams += `&sortOrder=${sortOrder}`;
      
      const response = await api.get(`/users?${queryParams}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Crear un nuevo usuario
  createUser: async (userData) => {
    try {
      const response = await api.post('/users', userData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Obtener un usuario por ID
  getUserById: async (userId) => {
    try {
      const response = await api.get(`/users/${userId}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Actualizar un usuario
  updateUser: async (userId, userData) => {
    try {
      const response = await api.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Cambiar contraseña
  changePassword: async (userId, passwordData) => {
    try {
      const response = await api.put(`/users/${userId}/change-password`, passwordData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Activar un usuario
  activateUser: async (userId) => {
    try {
      const response = await api.put(`/users/${userId}/activate`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Desactivar un usuario
  deactivateUser: async (userId) => {
    try {
      const response = await api.put(`/users/${userId}/deactivate`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  // Obtener todos los roles
  getAllRoles: async () => {
    try {
      const response = await api.get('/users/roles');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default UserService;
